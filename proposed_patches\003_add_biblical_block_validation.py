"""
CRITICAL FIX: Add Biblical Compliance to Block Validation
Biblical Reference: Multiple

This patch adds mandatory biblical compliance validation to the core block
validation process, ensuring no biblically non-compliant transactions can
be permanently recorded on the blockchain.

This is critical because the current block validator has no biblical law
enforcement, allowing violations to be immutably recorded.
"""

import time
import logging
from typing import Dict, Any, List, Optional, Tuple, Union

logger = logging.getLogger("onnyx.biblical_compliance.block_validation")

class BiblicalComplianceValidator:
    """
    Validates transactions and blocks for biblical compliance.
    """
    
    def __init__(self):
        # Import biblical compliance modules
        from proposed_patches.001_fix_jubilee_cycle import BiblicalJubileeCalculator
        from proposed_patches.002_implement_high_sabbaths import BiblicalHighSabbaths
        
        self.jubilee_calculator = BiblicalJubileeCalculator()
        self.high_sabbaths = BiblicalHighSabbaths()
        
        # Define forbidden operations during Sabbath periods
        self.sabbath_forbidden_operations = [
            'OP_MINE', 'OP_TRADE', 'OP_BUSINESS', 'OP_WORK',
            'OP_HARVEST', 'OP_MANUFACTURE', 'OP_TRANSPORT'
        ]
        
        # Define operations requiring gleaning contributions
        self.gleaning_required_operations = [
            'OP_HARVEST', 'OP_BUSINESS_PROFIT', 'OP_MINING_REWARD'
        ]
    
    def validate_transaction_biblical_compliance(self, transaction: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Validate a single transaction for biblical compliance.
        
        Args:
            transaction: Transaction dictionary
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            tx_timestamp = transaction.get('timestamp', int(time.time()))
            tx_op = transaction.get('op', '')
            tx_data = transaction.get('data', {})
            
            # 1. Check Sabbath violations (weekly and High Sabbaths)
            if self._is_sabbath_violation(tx_op, tx_timestamp):
                current_holiday = self.high_sabbaths.get_current_high_sabbath(tx_timestamp)
                if current_holiday:
                    return False, f"Transaction violates High Sabbath ({current_holiday.name}) - operation {tx_op} forbidden"
                else:
                    return False, f"Transaction violates weekly Sabbath - operation {tx_op} forbidden"
            
            # 2. Check usury violations
            if self._is_usury_violation(tx_op, tx_data):
                return False, f"Transaction violates usury prohibition - interest detected"
            
            # 3. Check gleaning law compliance
            if self._violates_gleaning_law(tx_op, tx_data):
                return False, f"Transaction violates gleaning law - required contribution missing"
            
            # 4. Check Sabbatical year restrictions
            if self._violates_sabbatical_year_restrictions(tx_op, tx_timestamp):
                return False, f"Transaction violates Sabbatical year restrictions - operation {tx_op} forbidden"
            
            # 5. Check Jubilee year compliance
            if self._violates_jubilee_year_restrictions(tx_op, tx_timestamp):
                return False, f"Transaction violates Jubilee year restrictions - operation {tx_op} forbidden"
            
            # 6. Check firstfruits compliance
            if self._violates_firstfruits_law(tx_op, tx_data):
                return False, f"Transaction violates firstfruits law - offering required"
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating biblical compliance: {e}")
            return False, f"Biblical compliance validation error: {str(e)}"
    
    def _is_sabbath_violation(self, operation: str, timestamp: int) -> bool:
        """Check if operation violates Sabbath (weekly or High Sabbath)."""
        # Check weekly Sabbath
        day_of_week = time.gmtime(timestamp).tm_wday
        hour_of_day = time.gmtime(timestamp).tm_hour
        
        # Saturday (5 in tm_wday where Monday=0) from Friday 6PM to Saturday 7PM
        is_weekly_sabbath = (
            (day_of_week == 4 and hour_of_day >= 18) or  # Friday 6PM+
            (day_of_week == 5 and hour_of_day < 19)      # Saturday until 7PM
        )
        
        # Check High Sabbaths
        is_high_sabbath = self.high_sabbaths.is_operation_forbidden_on_high_sabbath(operation, timestamp)
        
        if (is_weekly_sabbath or is_high_sabbath) and operation in self.sabbath_forbidden_operations:
            return True
            
        return False
    
    def _is_usury_violation(self, operation: str, tx_data: Dict[str, Any]) -> bool:
        """Check for usury violations (comprehensive detection)."""
        if operation != 'OP_LEND':
            return False
            
        # Check explicit interest rate
        if tx_data.get('interest_rate', 0) > 0:
            return True
            
        # Check for hidden fees that constitute usury
        fees = tx_data.get('fees', {})
        if isinstance(fees, dict):
            for fee_type, fee_amount in fees.items():
                if fee_amount > 0 and fee_type in ['service_fee', 'processing_fee', 'administrative_fee']:
                    # Any fee on a loan between covenant members is usury
                    return True
                    
        # Check for complex loan structures that hide interest
        if tx_data.get('repayment_amount', 0) > tx_data.get('principal_amount', 0):
            return True
            
        return False
    
    def _violates_gleaning_law(self, operation: str, tx_data: Dict[str, Any]) -> bool:
        """Check if transaction violates gleaning law."""
        if operation not in self.gleaning_required_operations:
            return False
            
        # Check if gleaning contribution was made
        gleaning_contribution = tx_data.get('gleaning_contribution', 0)
        total_amount = tx_data.get('amount', 0)
        
        # Require at least 2% for gleaning (biblical corner of field)
        required_gleaning = total_amount * 0.02
        
        return gleaning_contribution < required_gleaning
    
    def _violates_sabbatical_year_restrictions(self, operation: str, timestamp: int) -> bool:
        """Check if operation violates Sabbatical year restrictions."""
        if not self.jubilee_calculator.is_sabbatical_year():
            return False
            
        # During Sabbatical year, certain economic activities are restricted
        sabbatical_forbidden_operations = [
            'OP_HARVEST', 'OP_PLANT', 'OP_AGRICULTURAL_WORK',
            'OP_DEBT_COLLECTION', 'OP_FORECLOSURE'
        ]
        
        return operation in sabbatical_forbidden_operations
    
    def _violates_jubilee_year_restrictions(self, operation: str, timestamp: int) -> bool:
        """Check if operation violates Jubilee year restrictions."""
        if not self.jubilee_calculator.is_jubilee_year():
            return False
            
        # During Jubilee year, land sales and debt collection are forbidden
        jubilee_forbidden_operations = [
            'OP_LAND_SALE', 'OP_PROPERTY_TRANSFER', 'OP_DEBT_COLLECTION',
            'OP_FORECLOSURE', 'OP_PERMANENT_SALE'
        ]
        
        return operation in jubilee_forbidden_operations
    
    def _violates_firstfruits_law(self, operation: str, tx_data: Dict[str, Any]) -> bool:
        """Check if transaction violates firstfruits law."""
        if operation not in ['OP_HARVEST', 'OP_BUSINESS_PROFIT']:
            return False
            
        # Check if firstfruits offering was made
        firstfruits_offering = tx_data.get('firstfruits_offering', 0)
        
        # Firstfruits should be the first and best portion
        return firstfruits_offering == 0
    
    def validate_block_biblical_compliance(self, block: Union[Dict[str, Any], object]) -> Tuple[bool, Optional[str]]:
        """
        Validate entire block for biblical compliance.
        
        Args:
            block: Block dictionary or Block object
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Extract transactions from block
            if hasattr(block, 'transactions'):
                transaction_ids = block.transactions
                block_id = f"height {block.block_height}"
            else:
                transaction_ids = block.get('transactions', [])
                block_id = f"height {block.get('block_height', block.get('index', 'unknown'))}"
            
            # Validate each transaction
            for tx_id in transaction_ids:
                # Get transaction from database
                from shared.models.transaction import Transaction
                tx = Transaction.get_by_id(tx_id)
                
                if not tx:
                    return False, f"Transaction {tx_id} not found for biblical compliance validation"
                
                # Convert transaction to dict for validation
                tx_dict = {
                    'tx_id': tx.tx_id,
                    'op': tx.op,
                    'timestamp': tx.timestamp,
                    'data': tx.data if isinstance(tx.data, dict) else {}
                }
                
                # Validate biblical compliance
                is_valid, error_msg = self.validate_transaction_biblical_compliance(tx_dict)
                if not is_valid:
                    return False, f"Block {block_id} contains non-compliant transaction {tx_id}: {error_msg}"
            
            logger.info(f"Block {block_id} passed biblical compliance validation")
            return True, None
            
        except Exception as e:
            logger.error(f"Error validating block biblical compliance: {e}")
            return False, f"Biblical compliance validation error: {str(e)}"

# Patch implementation for existing block validator
def patch_block_validator():
    """
    Apply biblical compliance validation to existing block validator.
    """
    import sys
    import os
    
    # Import existing block validator
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'blockchain', 'vm'))
    from block_validator import validate_block, validate_block_safe, BlockValidationError
    
    biblical_validator = BiblicalComplianceValidator()
    
    def validate_block_with_biblical_compliance(block, previous_block=None, check_miner_reward=True, reward_amount=50):
        """Enhanced block validation with biblical compliance."""
        # First run existing validation
        original_validate_block(block, previous_block, check_miner_reward, reward_amount)
        
        # Then run biblical compliance validation
        is_compliant, error_msg = biblical_validator.validate_block_biblical_compliance(block)
        if not is_compliant:
            raise BlockValidationError(f"Biblical compliance violation: {error_msg}")
        
        return True
    
    def validate_block_safe_with_biblical_compliance(block, previous_block=None, check_miner_reward=True, reward_amount=50):
        """Safe block validation with biblical compliance."""
        try:
            validate_block_with_biblical_compliance(block, previous_block, check_miner_reward, reward_amount)
            return True, None
        except BlockValidationError as e:
            return False, str(e)
        except Exception as e:
            return False, f"Unexpected error: {str(e)}"
    
    # Store original functions
    original_validate_block = validate_block
    original_validate_block_safe = validate_block_safe
    
    # Replace with enhanced versions
    import blockchain.vm.block_validator as bv_module
    bv_module.validate_block = validate_block_with_biblical_compliance
    bv_module.validate_block_safe = validate_block_safe_with_biblical_compliance
    
    logger.info("✅ Applied biblical compliance to block validation")

if __name__ == "__main__":
    # Test the implementation
    validator = BiblicalComplianceValidator()
    
    # Test transaction validation
    test_transaction = {
        'tx_id': 'test_tx_001',
        'op': 'OP_MINE',
        'timestamp': int(time.time()),
        'data': {}
    }
    
    is_valid, error_msg = validator.validate_transaction_biblical_compliance(test_transaction)
    print(f"🔍 Test Transaction Validation:")
    print(f"Valid: {is_valid}")
    if error_msg:
        print(f"Error: {error_msg}")
    
    # Test usury detection
    usury_transaction = {
        'tx_id': 'test_usury_001',
        'op': 'OP_LEND',
        'timestamp': int(time.time()),
        'data': {
            'principal_amount': 1000,
            'interest_rate': 0.05  # 5% interest - forbidden
        }
    }
    
    is_valid, error_msg = validator.validate_transaction_biblical_compliance(usury_transaction)
    print(f"\n🔍 Usury Transaction Validation:")
    print(f"Valid: {is_valid}")
    if error_msg:
        print(f"Error: {error_msg}")
    
    print("\n✅ Biblical compliance validator ready for integration")
